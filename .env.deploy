SUPABASE_URL:https://nedfvxjbvuhzdwtcmyfa.supabase.co
SUPABASE_KEY:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5lZGZ2eGpidnVoemR3dGNteWZhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMyNzI1NzUsImV4cCI6MjA1ODg0ODU3NX0.c_-JYBfNJ8ooGKcl_ylp5HcQ1fWGbLv8Of65Daoh8rQ
OPENAI_BASE_URL:https://ark.cn-beijing.volces.com/api/v3
OPENAI_API_KEY:0b31ff1a-b750-4392-9d03-df29469244df
BYTEDANCE_BASE_URL:https://ark.cn-beijing.volces.com/api/v3
BYTEDANCE_API_KEY:0b31ff1a-b750-4392-9d03-df29469244df

TURSO_DATABASE_URL:libsql://aig-hakpaang.aws-ap-northeast-1.turso.io
TURSO_AUTH_TOKEN:***************************************************************************************************************************************************************************************************************************************************************************************
ACTIONS_CONFIG:[{"id":"dongni100","name":"懂你100","url":"https://www.dongni100.com","actions":[{"type":"screenshot","index":1,"value":"","selector":"div.img-warp"},{"type":"click","index":2,"value":"","selector":"div#tab-3"},{"type":"fill","index":3,"value":"","selector":"input[placeholder=\"得分\"]"},{"type":"click","index":4,"value":"","selector":"button:has-text(\"提交\")"}]},{"id":"gdyj","name":"光大阅卷","url":"http://pj.yixx.cn/njs_600/pc#/","actions":[{"type":"fetch","index":1,"value":"gdyj","selector":""},{"type":"click","index":2,"value":"","selector":"i.iconfont.icon-jianpan1"},{"type":"fill","index":3,"value":"","selector":"input[type=number]"},{"type":"click","index":4,"value":"","selector":"input[type=submit]"}]}]
SUBJECT_PROMPTS:[{"subject_id":"shs_geography","subject_name":"高中地理","prompt_text":"#地理主观题智能评分助手\n\n##定位\n专为高中地理教师设计的自动化阅卷工具，专注于根据定制化评分标准快速生成客观公正的评分结果。\n\n##核心能力\n1.结构化解析评分标准（包括得分点、关键词、分值权重）\n2.语义分析学生答案与标准答案的匹配度\n3.自动计算得分并生成标准化输出\n4.支持分层评分标准（如\"观点正确性/表述完整性/术语准确性\"多维度评估）\n\n##知识储备\n-人教版/湘教版等主流高中地理教材知识体系\n-高考地理评分细则规范\n-地理学科专业术语库（含常见易错表述识别）\n\n##输入：\n*包含学生作答内容的图片文件。\n*用户提供的文字版评分标准。\n\n##输出要求：\n严格按照以下JSON格式输出评分结果，不包含任何额外解释：\n\n{\"student_answer\":\"<从图片中提取学生答案内容>\",\"score\":<计算得到的最终数字得分>,\"grading_details\":\"<根据评分标准，详细说明得分点的获取情况和扣分点的具体原因，需清晰对应学生作答内容>\"}\n\n注意：\n-不要用Markdown代码块包裹\n-不要添加注释\n-字符串必须用双引号\n-数字不加引号\n-末尾禁止逗号"},{"subject_id":"shs_physics","subject_name":"高中物理","prompt_text":"#角色：智能高中物理阅卷助手\n\n##定位：\n你是一位能够识别图片中的学生手写或打印答案，并根据用户提供的评分标准进行高中物理题目自动阅卷的智能助手。\n\n##核心能力：\n1.**图像识别与内容提取**:准确识别用户上传的**学生答题图片**，提取其中的文字、公式、符号、图表等关键作答信息。\n2.**理解评分标准**:精确解读用户输入的**文字版评分标准**（包括参考答案、采分点、步骤分、扣分细则等）。\n3.**知识应用与分析**:运用高中物理知识，分析从图片中提取的学生作答内容的逻辑性、正确性、完整性和规范性。\n4.**标准比对与打分**:将提取的学生作答内容与评分标准进行严格匹配，判断每个得分点是否达成，计算最终得分。\n5.**生成结构化结果**:按照指定的JSON格式，整合提取的学生答案概要、得分和详细的评分说明。\n\n##知识储备：\n*精通中国高中物理课程知识体系。\n*熟悉物理题目的常见解法、步骤和评分规则。\n*具备OCR(光学字符识别)能力,能处理手写和印刷体物理公式、文字及符号。\n\n##工作流程：\n1.接收用户上传的**学生答题图片**和**文字形式的评分标准**。\n2.调用图像识别能力，提取图片中的学生作答内容。\n3.对照用户提供的评分标准，逐项评估提取出的学生作答内容。\n4.计算最终得分。\n5.生成包含学生答案概要、得分和评分细节的JSON对象。\n\n##输入：\n*包含学生作答内容的图片文件。\n*用户提供的文字版评分标准。\n\n##输出要求：\n严格按照以下JSON格式输出评分结果，不包含任何额外解释：\n\n{\n\"student_answer\":\"<从图片中提取学生答案内容>\",\n\"score\":<计算得到的最终数字得分>,\n\"grading_details\":\"<根据评分标准，详细说明得分点的获取情况和扣分点的具体原因，需清晰对应学生作答内容>\"\n}\n\n注意：\n-不要用Markdown代码块包裹\n-不要添加注释\n-字符串必须用双引号\n-数字不加引号\n-末尾禁止逗号\n"}]

# 应用版本配置
APP_VERSION:v0.0.8
APP_DOWNLOAD_URL:https://shanzhulab.cn/
APP_FORCE_UPDATE:false
APP_UPDATE_LOG:更新日志：
1.增强自定义模式适配性
2.支持离线安装组件（在工具菜单中）
3.增加循环阅卷均分计算功能
4.优化用户体验

可在山竹阅卷官方网站 https://shanzhulab.cn 
或者公众号获取最新版本下载链接
