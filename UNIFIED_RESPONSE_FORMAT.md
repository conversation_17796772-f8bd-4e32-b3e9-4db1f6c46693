# Analysis API 统一响应格式实现

## 概述

根据需求，Analysis API 已经重构为统一的响应格式，所有三种分析模式都返回相同的结构化数据格式，简化了客户端的处理逻辑。

## 主要变更

### 1. 统一响应结构

**新的响应格式**：
```json
{
  "id": "chatcmpl-xxx",
  "analysis": {
    "student_answer": "学生答案内容",
    "score": 85,
    "grading_details": "详细的评分说明"
  },
  "balance": 9700
}
```

**移除的字段**：
- `ocr_result` - 不再单独返回OCR结果
- `content` - 不再返回原始分析内容
- `analysis_mode` - 不再返回分析模式信息
- `models` - 不再返回使用的模型列表

### 2. 数据结构重构

#### 新增统一结构
```go
// AnalysisResult 统一的分析结果结构
type AnalysisResult struct {
    StudentAnswer  string `json:"student_answer"`  // 学生答案内容
    Score          int    `json:"score"`           // 最终数字得分
    GradingDetails string `json:"grading_details"` // 详细评分说明
}

// SimplifiedResponse 简化的响应结构
type SimplifiedResponse struct {
    ID       string          `json:"id"`
    Analysis *AnalysisResult `json:"analysis,omitempty"`
    Balance  int             `json:"balance"`
}
```

#### 内部结构重命名
```go
// InternalAnalysisResult 内部分析结果结构（避免命名冲突）
type InternalAnalysisResult struct {
    OcrResult        string
    AnalysisResponse *openai.ChatCompletionResponse
    OcrResponse      *openai.ChatCompletionResponse
    Mode             AnalysisMode
    Models           []string
    TotalCost        int
}
```

### 3. 提示词简化

**系统提示词包含JSON格式要求**，用户提示词直接使用请求体中的 `text` 字段：

#### 专业模式
```go
// 构建分析提示词，直接使用请求体中的Text
analysisPrompt := req.RequestBody.Text
```

#### 标准/经济模式
```go
// 构建分析提示词，直接使用请求体中的Text
analysisPrompt := req.RequestBody.Text
```

### 4. 三种模式的数据处理

#### 专业模式 (Professional Mode)
- **OCR阶段**: 使用 `doubao-1-5-vision-pro-32k-250115` 识别图片
- **分析阶段**: 使用 `deepseek-v3-250324` 分析OCR结果
- **数据映射**: OCR结果填入 `student_answer` 字段

#### 标准模式 (Standard Mode)
- **直接分析**: 使用 `doubao-seed-1-6-250615` 进行多模态分析
- **结构化输出**: 使用 `ResponseFormat` 确保JSON格式
- **数据映射**: 模型直接输出包含 `student_answer` 的结构

#### 经济模式 (Economy Mode)
- **快速分析**: 使用 `doubao-seed-1-6-flash-250615` 进行快速分析
- **结构化输出**: 使用 `ResponseFormat` 确保JSON格式
- **数据映射**: 与标准模式相同的处理方式

### 5. JSON解析增强

#### 专业模式特殊处理
```go
func extractJSONFromContent(content string) string {
    // 处理markdown代码块
    if strings.Contains(content, "```json") {
        // 提取代码块中的JSON
    }
    
    // 提取JSON对象
    start := strings.Index(content, "{")
    end := strings.LastIndex(content, "}")
    if start != -1 && end != -1 && end > start {
        return strings.TrimSpace(content[start : end+1])
    }
    
    return content
}
```

#### 统一解析逻辑
```go
func parseStructuredAnalysisResult(content string, mode AnalysisMode, ocrResult string) (*AnalysisResult, error) {
    switch mode {
    case ProfessionalMode:
        // 解析专业模式的JSON，OCR结果填入student_answer
        return &AnalysisResult{
            StudentAnswer:  ocrResult,
            Score:          tempResult.Score,
            GradingDetails: tempResult.GradingDetails,
        }, nil
        
    case StandardMode, EconomyMode:
        // 直接解析标准格式的JSON
        var result AnalysisResult
        json.Unmarshal([]byte(content), &result)
        return &result, nil
    }
}
```

## 响应示例对比

### 重构前（复杂格式）
```json
{
  "id": "chatcmpl-xxx",
  "ocr_result": "学生答案...",
  "content": "{\"score\": 85, \"grading_details\": \"...\"}",
  "analysis": {...},
  "balance": 9500,
  "analysis_mode": "professional",
  "models": ["model1", "model2"]
}
```

### 重构后（统一格式）
```json
{
  "id": "chatcmpl-xxx",
  "analysis": {
    "student_answer": "学生答案...",
    "score": 85,
    "grading_details": "详细评分说明..."
  },
  "balance": 9500
}
```

## 优势

### 1. 简化客户端处理
- **统一接口**: 所有模式使用相同的响应结构
- **减少字段**: 移除冗余信息，专注核心数据
- **类型一致**: `analysis` 字段始终是相同的结构

### 2. 提高可维护性
- **代码简化**: 减少条件判断和特殊处理
- **结构清晰**: 明确的数据流和转换逻辑
- **易于扩展**: 新增模式只需实现统一接口

### 3. 性能优化
- **减少数据传输**: 移除不必要的字段
- **简化解析**: 客户端处理逻辑更简单
- **内存效率**: 更紧凑的数据结构

## 向后兼容性

虽然响应格式发生了重大变化，但核心分析数据仍然完整：
- 学生答案内容 → `analysis.student_answer`
- 评分结果 → `analysis.score`
- 详细说明 → `analysis.grading_details`
- 用户余额 → `balance`

客户端需要更新以适应新的响应格式，但所有必要的信息都得到了保留。

## 使用建议

1. **客户端更新**: 修改解析逻辑以使用新的 `analysis` 字段
2. **错误处理**: 检查 `analysis` 字段是否存在
3. **数据验证**: 验证 `score` 和其他必要字段
4. **向前兼容**: 为未来可能的字段扩展做好准备

## 技术细节

- **线程安全**: 继续使用线程安全的客户端管理器
- **错误容错**: 解析失败时返回 `null` 但不影响其他字段
- **日志记录**: 详细的错误日志便于调试
- **性能监控**: 保留成本计算和消费记录功能
