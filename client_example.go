package main

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// 示例：演示线程安全的客户端获取
func ExampleThreadSafeClientUsage() {
	// 模拟并发请求
	var wg sync.WaitGroup
	numGoroutines := 10
	// 启动多个 goroutine 同时请求客户端
	for i := range numGoroutines {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			// 模拟不同的模型请求
			models := []string{
				"deepseek-v3-250324",
				"doubao-1-5-vision-pro-32k-250115",
			}
			for _, model := range models {
				// 获取客户端（线程安全）
				client, err := GetClientForModel(model)
				if err != nil {
					log.Printf("Goroutine %d: 获取客户端失败 for model %s: %v", id, model, err)
					continue
				}
				log.Printf("Goroutine %d: 成功获取客户端 for model %s", id, model)
				// 模拟使用客户端
				ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer cancel()
				// 这里可以调用实际的API，但为了示例我们只是模拟
				_ = client
				_ = ctx
				// 模拟一些处理时间
				time.Sleep(10 * time.Millisecond)
			}
		}(i)
	}
	// 等待所有 goroutine 完成
	wg.Wait()
	fmt.Println("所有并发请求完成")
}

// 示例：演示客户端重置功能
func ExampleClientReset() {
	fmt.Println("=== 客户端重置示例 ===")
	// 首次获取客户端
	client1, err := GetClientForModel("deepseek-v3-250324")
	if err != nil {
		log.Printf("首次获取客户端失败: %v", err)
		return
	}
	fmt.Printf("首次获取客户端成功: %p\n", client1)
	// 再次获取相同模型的客户端（应该返回相同实例）
	client2, err := GetClientForModel("deepseek-v3-250324")
	if err != nil {
		log.Printf("再次获取客户端失败: %v", err)
		return
	}
	fmt.Printf("再次获取客户端: %p\n", client2)
	if client1 == client2 {
		fmt.Println("✓ 客户端实例相同（符合预期）")
	} else {
		fmt.Println("✗ 客户端实例不同（不符合预期）")
	}
	// 重置所有客户端
	fmt.Println("重置所有客户端...")
	ResetAllClients()
	// 重置后再次获取客户端（应该是新实例）
	client3, err := GetClientForModel("deepseek-v3-250324")
	if err != nil {
		log.Printf("重置后获取客户端失败: %v", err)
		return
	}
	fmt.Printf("重置后获取客户端: %p\n", client3)
	if client1 != client3 {
		fmt.Println("✓ 重置后客户端实例不同（符合预期）")
	} else {
		fmt.Println("✗ 重置后客户端实例相同（不符合预期）")
	}
}

// 示例：演示错误处理
func ExampleErrorHandling() {
	fmt.Println("=== 错误处理示例 ===")
	// 尝试获取不支持的模型
	_, err := GetClientForModel("unsupported-model")
	if err != nil {
		fmt.Printf("✓ 正确处理不支持的模型: %v\n", err)
	}
	// 注意：在实际环境中，如果环境变量未设置，也会返回错误
	// 这里假设环境变量已正确配置
}

// 如果你想运行这些示例，可以取消注释下面的 main 函数
/*
func main() {
	fmt.Println("=== 线程安全客户端管理器示例 ===")
	
	// 运行并发示例
	ExampleThreadSafeClientUsage()
	
	// 运行重置示例
	ExampleClientReset()
	
	// 运行错误处理示例
	ExampleErrorHandling()
}
*/
