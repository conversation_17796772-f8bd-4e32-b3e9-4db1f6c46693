package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strings"

	"github.com/sashabaranov/go-openai"
	"github.com/supabase-community/auth-go/types"
)

// 字节的模型列表
var BYTEDANCE_MODELS_MAP = map[string]int{
	"deepseek-v3-250324":               1,
	"deepseek-r1-250120":               2,
	"doubao-1-5-vision-pro-32k-250115": 3,
	"doubao-1.5-vision-pro-250328":     4,
	"doubao-seed-1-6-250615":           5,
	"doubao-seed-1-6-flash-250615":     6,
}



// 默认成本倍率
const DEFAULT_COST_MULTIPLIER = 2

// 全局模型价格映射
var MODEL_PRICING_MAP = map[string]ModelPricing{
	// 字节模型价格
	"doubao-seed-1-6-250615":           {PromptPrice: 0.0008 / 1000, CompletionPrice: 0.0008 / 1000},
	"doubao-seed-1-6-flash-250615":     {PromptPrice: 0.00015 / 1000, CompletionPrice: 0.00015 / 1000},
	"doubao-1.5-vision-pro-250328":     {PromptPrice: 0.003 / 1000, CompletionPrice: 0.009 / 1000},
	"doubao-1-5-vision-pro-32k-250115": {PromptPrice: 0.003 / 1000, CompletionPrice: 0.009 / 1000},
	"deepseek-v3-250324":               {PromptPrice: 0.002 / 1000, CompletionPrice: 0.008 / 1000},
	"deepseek-r1-250120":               {PromptPrice: 0.004 / 1000, CompletionPrice: 0.016 / 1000},
}

// 默认价格
var DEFAULT_PRICING = ModelPricing{PromptPrice: 0.003 / 1000, CompletionPrice: 0.009 / 1000}

// AnalysisMode 分析模式枚举
type AnalysisMode string

const (
	// ProfessionalMode 专业精批模式：OCR + 分析
	ProfessionalMode AnalysisMode = "professional"
	// StandardMode 智能均衡模式：直接多模态分析
	StandardMode AnalysisMode = "standard"
	// EconomyMode 经济速览模式：快速多模态分析
	EconomyMode AnalysisMode = "economy"
)

// ChatRequest 聊天请求结构
type ChatRequest struct {
	Model        string       `json:"model"`
	Text         string       `json:"text"`
	PromptKey    string       `json:"prompt_key"`
	Content      string       `json:"content"`
	ContentType  string       `json:"content_type"`
	Temperature  float32      `json:"temperature"`
	AnalysisMode AnalysisMode `json:"analysis_mode,omitempty"` // 分析模式，仅用于analysis接口
}

// OpenAIImageContent 表示OpenAI API图像内容
type OpenAIImageContent struct {
	URL string `json:"url"`
}

// OpenAIChoice 表示OpenAI API响应选择
type OpenAIChoice struct {
	Message struct {
		Content string `json:"content"`
	} `json:"message"`
}

// AnalysisResult 统一的分析结果结构
type AnalysisResult struct {
	StudentAnswer  string `json:"student_answer"`  // 学生答案内容（专业模式为OCR结果，其他模式为从图片提取的内容）
	Score          int    `json:"score"`           // 计算得到的最终数字得分
	GradingDetails string `json:"grading_details"` // 详细说明得分点和扣分点
}

// SimplifiedResponse 表示简化的响应
type SimplifiedResponse struct {
	ID       string          `json:"id"`
	Analysis *AnalysisResult `json:"analysis,omitempty"` // 结构化分析结果
	Balance  int             `json:"balance"`
}

// ModelPricing 表示模型价格
type ModelPricing struct {
	PromptPrice     float64
	CompletionPrice float64
}

// GetModelPricing 获取特定模型的价格
func GetModelPricing(model string) ModelPricing {
	// 根据模型名称选择价格
	pricing, exists := MODEL_PRICING_MAP[model]
	if !exists {
		return DEFAULT_PRICING
	}
	return pricing
}

func ChatHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "", http.StatusUnauthorized)
		return
	}
	// 检查用户钱包余额
	firstBalance, err := pg.GetUserBalance(user.ID.String())
	if err != nil {
		log.Printf("获取余额失败: %v\n", err)
		http.Error(w, "Failed to get balance", http.StatusInternalServerError)
		return
	}
	// 检查余额是否足够
	if firstBalance <= 100 { // 0.1元 = 100积分
		http.Error(w, "Insufficient balance", http.StatusPaymentRequired)
		return
	}
	// 解析请求体
	var requestBody ChatRequest
	if err = json.NewDecoder(r.Body).Decode(&requestBody); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}
	// 验证请求
	if requestBody.Text == "" || requestBody.PromptKey == "" || requestBody.ContentType == "" {
		http.Error(w, "Invalid request: missing required fields", http.StatusBadRequest)
		return
	}
	// 使用默认模型如果未指定
	model := requestBody.Model
	if model == "" {
		model = "deepseek-v3-250324"
	}
	// 使用默认温度如果未指定
	if requestBody.Temperature == 0 {
		requestBody.Temperature = 0.3
	}
	// 根据PromptKey参数从数据库选择不同的systemPrompt
	requestBody.PromptKey, _ = pg.GetSystemPromptForSubject(requestBody.PromptKey)
	// 创建上下文
	ctx := r.Context()
	// 发送聊天完成请求
	openaiResponse, err := CreateChatCompletion(ctx, requestBody)
	if err != nil {
		http.Error(w, fmt.Sprintf("API error: %v", err), http.StatusInternalServerError)
		return
	}
	// 获取模型价格
	pricing := GetModelPricing(model)
	// 计算API调用成本（积分制）
	cost := calculateAPICostInPoints(openaiResponse.Usage, pricing)
	// 创建消费元数据
	metadata := map[string]any{
		"model":             model,
		"prompt_tokens":     openaiResponse.Usage.PromptTokens,
		"completion_tokens": openaiResponse.Usage.CompletionTokens,
		"total_tokens":      openaiResponse.Usage.TotalTokens,
		"prompt_key":        requestBody.PromptKey,
		"cost_multiplier":   DEFAULT_COST_MULTIPLIER,
	}
	// 记录消费
	consumeResult, err := pg.CheckAndConsume(
		user.ID.String(),
		cost,
		"使用"+model+"模型",
		metadata,
	)
	if err != nil {
		// 如果记录消费失败，记录错误但仍然返回响应
		log.Printf("记录消费失败: %v\n", err)
	}
	// 获取更新后的余额
	var balance int
	if consumeResult != nil && consumeResult["success"].(bool) {
		balance = consumeResult["balance"].(int)
	} else {
		// 如果消费失败或未记录消费，尝试获取当前余额
		balance, err = pg.GetUserBalance(user.ID.String())
	}
	// 提取需要的字段
	simplifiedResponse := SimplifiedResponse{
		ID:      openaiResponse.ID,
		Balance: balance,
	}
	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	// 检查是否有刷新令牌
	refreshToken := r.Header.Get("X-Refresh-Token")
	if refreshToken != "" {
		// 调用Supabase Auth API进行令牌刷新
		resp, err := authClient.client.RefreshToken(refreshToken)
		if err == nil && resp != nil {
			// 将新的访问令牌放入响应头
			w.Header().Set("X-New-Access-Token", resp.AccessToken)
		}
	}
	json.NewEncoder(w).Encode(simplifiedResponse)
}

// 计算API调用成本（积分制）
func calculateAPICostInPoints(usage openai.Usage, pricing ModelPricing) int {
	// 计算提示和完成的成本（元）
	promptCost := float64(usage.PromptTokens) * pricing.PromptPrice
	completionCost := float64(usage.CompletionTokens) * pricing.CompletionPrice
	// 应用成本倍率
	totalCostYuan := (promptCost + completionCost) * DEFAULT_COST_MULTIPLIER
	// 转换为积分（1元=1000积分）并向上取整
	totalCostPoints := max(int(math.Ceil(totalCostYuan*1000)), 1)
	return totalCostPoints
}

// containsKey 检查字符串是否在map中
func containsKey(models map[string]int, items string) bool {
	if _, ok := models[items]; ok {
		return true
	}
	return false
}

func modelId(key string) int {
	if v, ok := BYTEDANCE_MODELS_MAP[key]; ok {
		return v
	}
	return 0
}

// AnalysisRequest 表示分析请求的内部结构
type AnalysisRequest struct {
	User        *types.UserResponse
	RequestBody ChatRequest
	Context     context.Context
}

// InternalAnalysisResult 表示分析结果的内部结构
type InternalAnalysisResult struct {
	OcrResult        string                         // OCR结果（仅专业模式）
	AnalysisResponse *openai.ChatCompletionResponse // 主要分析响应
	OcrResponse      *openai.ChatCompletionResponse // OCR响应（仅专业模式）
	Mode             AnalysisMode                   // 使用的分析模式
	Models           []string                       // 使用的模型列表
	TotalCost        int                            // 总成本
}

// validateAnalysisRequest 验证分析请求
func validateAnalysisRequest(w http.ResponseWriter, r *http.Request) (*AnalysisRequest, bool) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return nil, false
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "", http.StatusUnauthorized)
		return nil, false
	}
	// 检查用户钱包余额
	firstBalance, err := pg.GetUserBalance(user.ID.String())
	if err != nil {
		log.Printf("获取余额失败: %v\n", err)
		http.Error(w, "Failed to get balance", http.StatusInternalServerError)
		return nil, false
	}
	// 检查余额是否足够
	if firstBalance <= 100 { // 0.1元 = 100积分
		http.Error(w, "Insufficient balance", http.StatusPaymentRequired)
		return nil, false
	}
	// 解析请求体
	var requestBody ChatRequest
	if err = json.NewDecoder(r.Body).Decode(&requestBody); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return nil, false
	}
	// 验证请求
	if requestBody.Content == "" || requestBody.PromptKey == "" {
		http.Error(w, "Invalid request: content, prompt_key are required", http.StatusBadRequest)
		return nil, false
	}

	// 设置默认分析模式
	if requestBody.AnalysisMode == "" {
		requestBody.AnalysisMode = StandardMode // 默认使用智能均衡模式
	}

	// 验证分析模式
	switch requestBody.AnalysisMode {
	case ProfessionalMode, StandardMode, EconomyMode:
		// 有效模式
	default:
		http.Error(w, "Invalid analysis_mode. Must be one of: professional, standard, economy", http.StatusBadRequest)
		return nil, false
	}
	return &AnalysisRequest{
		User:        user,
		RequestBody: requestBody,
		Context:     r.Context(),
	}, true
}

// performAnalysisByMode 根据模式执行分析
func performAnalysisByMode(w http.ResponseWriter, req *AnalysisRequest) (*InternalAnalysisResult, bool) {
	switch req.RequestBody.AnalysisMode {
	case ProfessionalMode:
		return performProfessionalAnalysis(w, req)
	case StandardMode:
		return performStandardAnalysis(w, req)
	case EconomyMode:
		return performEconomyAnalysis(w, req)
	default:
		http.Error(w, "Invalid analysis mode", http.StatusBadRequest)
		return nil, false
	}
}

// performProfessionalAnalysis 专业精批模式：OCR + 分析
func performProfessionalAnalysis(w http.ResponseWriter, req *AnalysisRequest) (*InternalAnalysisResult, bool) {
	// 第一步：OCR处理
	ocrModel := "doubao-1-5-vision-pro-32k-250115"
	ocrClient, err := GetClientForModel(ocrModel)
	if err != nil {
		http.Error(w, "Failed to create OCR API client", http.StatusInternalServerError)
		return nil, false
	}
	// OCR提示词
	ocrUserPrompt := `Role:教育专用手写OCR引擎
Action:准确识别图片中学生手写答题内容
Constraint:仅识别中文和英文,忽略划掉的文本,数学公式用LaTeX格式,如 (E=mc^2)
Input:用户上传的学生答题图片
Ouput: 只输出识别的内容，不要返回其他内容`
	ocrResponse, err := ocrClient.CreateChatCompletionWithImage(
		req.Context,
		ocrModel,
		"",
		ocrUserPrompt,
		req.RequestBody.Content,
		0.3,
	)
	if err != nil {
		http.Error(w, fmt.Sprintf("OCR API error: %v", err), http.StatusInternalServerError)
		return nil, false
	}
	ocrResult := ocrResponse.Choices[0].Message.Content
	// 第二步：分析处理
	analysisModel := "deepseek-v3-250324"
	analysisClient, err := GetClientForModel(analysisModel)
	if err != nil {
		http.Error(w, "Failed to create analysis API client", http.StatusInternalServerError)
		return nil, false
	}
	// 获取系统提示词
	systemPrompt, _ := pg.GetSystemPromptForSubject(req.RequestBody.PromptKey)
	// 构建分析提示词，直接使用请求体中的Text
	analysisPrompt := req.RequestBody.Text
	temperature := req.RequestBody.Temperature
	if temperature == 0 {
		temperature = 0.3
	}
	analysisResponse, err := analysisClient.CreateChatCompletionWithText(
		req.Context,
		analysisModel,
		systemPrompt,
		analysisPrompt,
		temperature,
	)
	if err != nil {
		http.Error(w, fmt.Sprintf("Analysis API error: %v", err), http.StatusInternalServerError)
		return nil, false
	}
	return &InternalAnalysisResult{
		OcrResult:        ocrResult,
		AnalysisResponse: analysisResponse,
		OcrResponse:      ocrResponse,
		Mode:             ProfessionalMode,
		Models:           []string{ocrModel, analysisModel},
	}, true
}

// performStandardAnalysis 智能均衡模式：直接多模态分析
func performStandardAnalysis(w http.ResponseWriter, req *AnalysisRequest) (*InternalAnalysisResult, bool) {
	model := "doubao-seed-1-6-250615"
	client, err := GetClientForModel(model)
	if err != nil {
		http.Error(w, "Failed to create API client", http.StatusInternalServerError)
		return nil, false
	}
	// 获取系统提示词
	systemPrompt, _ := pg.GetSystemPromptForSubject(req.RequestBody.PromptKey)
	// 构建分析提示词，直接使用请求体中的Text
	analysisPrompt := req.RequestBody.Text
	temperature := req.RequestBody.Temperature
	if temperature == 0 {
		temperature = 0.3
	}
	// 创建响应格式
	responseFormat := createStandardAnalysisResponseFormat()
	response, err := client.CreateChatCompletionWithImageAndResponseFormat(
		req.Context,
		model,
		systemPrompt,
		analysisPrompt,
		req.RequestBody.Content,
		temperature,
		responseFormat,
	)
	if err != nil {
		http.Error(w, fmt.Sprintf("API error: %v", err), http.StatusInternalServerError)
		return nil, false
	}
	return &InternalAnalysisResult{
		AnalysisResponse: response,
		Mode:             StandardMode,
		Models:           []string{model},
	}, true
}

// performEconomyAnalysis 经济速览模式：快速多模态分析
func performEconomyAnalysis(w http.ResponseWriter, req *AnalysisRequest) (*InternalAnalysisResult, bool) {
	model := "doubao-seed-1-6-flash-250615"
	client, err := GetClientForModel(model)
	if err != nil {
		http.Error(w, "Failed to create API client", http.StatusInternalServerError)
		return nil, false
	}
	// 获取系统提示词
	systemPrompt, _ := pg.GetSystemPromptForSubject(req.RequestBody.PromptKey)
	// 构建分析提示词，直接使用请求体中的Text
	analysisPrompt := req.RequestBody.Text
	temperature := req.RequestBody.Temperature
	if temperature == 0 {
		temperature = 0.3
	}
	// 创建响应格式
	responseFormat := createStandardAnalysisResponseFormat()
	response, err := client.CreateChatCompletionWithImageAndResponseFormat(
		req.Context,
		model,
		systemPrompt,
		analysisPrompt,
		req.RequestBody.Content,
		temperature,
		responseFormat,
	)
	if err != nil {
		http.Error(w, fmt.Sprintf("API error: %v", err), http.StatusInternalServerError)
		return nil, false
	}
	return &InternalAnalysisResult{
		AnalysisResponse: response,
		Mode:             EconomyMode,
		Models:           []string{model},
	}, true
}

// calculateCostAndConsume 计算成本并记录消费
func calculateCostAndConsume(req *AnalysisRequest, result *InternalAnalysisResult) (int, error) {
	var totalCost int
	var metadata map[string]any

	switch result.Mode {
	case ProfessionalMode:
		// 专业模式：OCR + 分析两个模型的成本
		ocrModel := result.Models[0]
		analysisModel := result.Models[1]

		ocrPricing := GetModelPricing(ocrModel)
		ocrCost := calculateAPICostInPoints(result.OcrResponse.Usage, ocrPricing)

		analysisPricing := GetModelPricing(analysisModel)
		analysisCost := calculateAPICostInPoints(result.AnalysisResponse.Usage, analysisPricing)

		totalCost = ocrCost + analysisCost

		metadata = map[string]any{
			"m":   string(result.Mode),
			"om":  modelId(ocrModel),                              // ocr_model
			"am":  modelId(analysisModel),                         // analysis_model
			"opt": result.OcrResponse.Usage.PromptTokens,          // ocr_prompt_tokens
			"oct": result.OcrResponse.Usage.CompletionTokens,      // ocr_completion_tokens
			"apt": result.AnalysisResponse.Usage.PromptTokens,     // analysis_prompt_tokens
			"act": result.AnalysisResponse.Usage.CompletionTokens, // analysis_completion_tokens
			"pk":  req.RequestBody.PromptKey,                      // prompt_key
			"dcm": DEFAULT_COST_MULTIPLIER,                        // cost_multiplier
		}

	case StandardMode, EconomyMode:
		// 标准模式和经济模式：单个模型的成本
		model := result.Models[0]
		pricing := GetModelPricing(model)
		totalCost = calculateAPICostInPoints(result.AnalysisResponse.Usage, pricing)

		metadata = map[string]any{
			"m":   string(result.Mode),
			"pt":  result.AnalysisResponse.Usage.PromptTokens,     // prompt_tokens
			"ct":  result.AnalysisResponse.Usage.CompletionTokens, // completion_tokens
			"pk":  req.RequestBody.PromptKey,                      // prompt_key
			"dcm": DEFAULT_COST_MULTIPLIER,                        // cost_multiplier
		}
	}
	result.TotalCost = totalCost
	// 记录消费
	consumeResult, err := pg.CheckAndConsume(
		req.User.ID.String(),
		totalCost,
		"",
		metadata,
	)
	if err != nil {
		// 如果记录消费失败，记录错误但仍然返回响应
		log.Printf("记录消费失败: %v\n", err)
	}
	// 获取更新后的余额
	var balance int
	if consumeResult != nil && consumeResult["success"].(bool) {
		balance = consumeResult["balance"].(int)
	} else {
		// 如果消费失败或未记录消费，尝试获取当前余额
		balance, _ = pg.GetUserBalance(req.User.ID.String())
	}
	return balance, nil
}

// createStandardAnalysisResponseFormat 创建标准/经济模式的响应格式
func createStandardAnalysisResponseFormat() *openai.ChatCompletionResponseFormat {
	return &openai.ChatCompletionResponseFormat{
		Type: openai.ChatCompletionResponseFormatTypeJSONObject,
	}
}

// parseStructuredAnalysisResult 解析结构化分析结果
func parseStructuredAnalysisResult(content string, mode AnalysisMode, ocrResult string) (*AnalysisResult, error) {
	switch mode {
	case ProfessionalMode:
		// 专业模式：deepseek-v3-250324 不支持模型级结构化输出，需要手动解析JSON
		// 可能包含markdown代码块，需要提取JSON部分
		jsonContent := extractJSONFromContent(content)
		var tempResult struct {
			Score          int    `json:"score"`
			GradingDetails string `json:"grading_details"`
		}
		if err := json.Unmarshal([]byte(jsonContent), &tempResult); err != nil {
			log.Printf("专业模式JSON解析失败: %v, 内容: %s", err, content)
			return nil, err
		}
		return &AnalysisResult{
			StudentAnswer:  ocrResult, // 专业模式使用OCR结果作为学生答案
			Score:          tempResult.Score,
			GradingDetails: tempResult.GradingDetails,
		}, nil

	case StandardMode, EconomyMode:
		// 标准/经济模式：支持结构化输出
		var result AnalysisResult
		if err := json.Unmarshal([]byte(content), &result); err != nil {
			log.Printf("标准/经济模式JSON解析失败: %v, 内容: %s", err, content)
			return nil, err
		}
		return &result, nil

	default:
		return nil, fmt.Errorf("不支持的分析模式: %s", mode)
	}
}

// extractJSONFromContent 从内容中提取JSON部分
func extractJSONFromContent(content string) string {
	// 如果内容包含markdown代码块，提取其中的JSON
	if strings.Contains(content, "```json") {
		start := strings.Index(content, "```json")
		if start != -1 {
			start += 7 // 跳过 "```json"
			end := strings.Index(content[start:], "```")
			if end != -1 {
				return strings.TrimSpace(content[start : start+end])
			}
		}
	}
	// 如果内容包含 { 和 }，尝试提取JSON对象
	start := strings.Index(content, "{")
	end := strings.LastIndex(content, "}")
	if start != -1 && end != -1 && end > start {
		return strings.TrimSpace(content[start : end+1])
	}
	// 否则返回原始内容
	return content
}

// buildAnalysisResponse 构建并发送响应
func buildAnalysisResponse(w http.ResponseWriter, r *http.Request, result *InternalAnalysisResult, balance int) {
	content := result.AnalysisResponse.Choices[0].Message.Content
	// 解析结构化分析结果
	analysisData, err := parseStructuredAnalysisResult(content, result.Mode, result.OcrResult)
	if err != nil {
		log.Printf("解析结构化分析结果失败: %v", err)
		// 如果解析失败，仍然返回原始内容
		analysisData = nil
	}
	// 提取需要的字段
	simplifiedResponse := SimplifiedResponse{
		ID:       result.AnalysisResponse.ID,
		Analysis: analysisData, // 结构化分析结果
		Balance:  balance,
	}
	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	// 检查是否有刷新令牌
	refreshToken := r.Header.Get("X-Refresh-Token")
	if refreshToken != "" {
		// 调用Supabase Auth API进行令牌刷新
		resp, err := authClient.client.RefreshToken(refreshToken)
		if err == nil && resp != nil {
			// 将新的访问令牌放入响应头
			w.Header().Set("X-New-Access-Token", resp.AccessToken)
		}
	}
	json.NewEncoder(w).Encode(simplifiedResponse)
}

// AnalysisHandler 处理分析请求
func AnalysisHandler(w http.ResponseWriter, r *http.Request) {
	// 验证请求
	req, ok := validateAnalysisRequest(w, r)
	if !ok {
		return
	}
	// 根据模式执行分析
	result, ok := performAnalysisByMode(w, req)
	if !ok {
		return
	}
	// 计算成本并记录消费
	balance, err := calculateCostAndConsume(req, result)
	if err != nil {
		log.Printf("计算成本失败: %v\n", err)
	}
	// 构建并发送响应
	buildAnalysisResponse(w, r, result, balance)
}
